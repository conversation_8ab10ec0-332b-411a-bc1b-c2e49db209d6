import { FullScreenImage } from "@/app/components/full-screen";
import { GameId } from "@/app/constants";
import { imageUrl } from "@/utils/image-url";
import { BalanceGameApp } from "../game-app/balance-game-app";
import { GameTitle } from "../components/game-title";

interface PlayStageProps {
  gameId: GameId;
  onGameEnd: (score: number) => void;
}

export const PlayStage = ({ gameId, onGameEnd }: PlayStageProps) => {
  return (
    <>
      <FullScreenImage src={imageUrl("/screen-game.png")} />
      <div className="relative z-0 flex flex-col items-center pt-[4vw]">
        <div className="w-[73vw] h-[23.5vw] flex justify-center items-center">
          <GameTitle gameId={gameId} />
        </div>
        <div>
          <BalanceGameApp onGameEnd={onGameEnd} />
        </div>
      </div>
    </>
  );
};
