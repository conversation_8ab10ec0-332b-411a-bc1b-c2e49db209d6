"use client";
import type { GameList } from "@/app/api/game/list/query";
import { GameId } from "@/app/constants";
import { useMutation, useQuery } from "@tanstack/react-query";
import { useCallback, useState, useEffect } from "react";
import { GameState } from "./game-entries";
import { GameResult } from "./game-result";
import { useImagePreloader } from "./hooks/use-image-preloader";
import { GameTitle } from "./components";
import {
  LoadingStage,
  NicknameStage,
  EntranceStage,
  InstructionStage,
  PlayStage,
} from "./stages";

enum Stage {
  Loading = "loading",
  Nickname = "nickname",
  Entrance = "entrance",
  Instruction = "Instruction",
  Play = "play",
  Result = "Result",
}

const GamePage = () => {
  const [nickname, setNickname] = useState<string>("");

  const [stage, setStage] = useState<Stage>(Stage.Loading);
  const [gameId, setGameId] = useState<GameId>("balance");
  const [score, setScore] = useState(0);
  const [gameRecordId, setGameRecordId] = useState<string>("");

  const { progress, isComplete, preloadImages } = useImagePreloader();

  // Start preloading images when component mounts
  useEffect(() => {
    preloadImages();
  }, [preloadImages]);

  // Move to nickname stage when preloading is complete
  useEffect(() => {
    if (isComplete && stage === Stage.Loading) {
      setStage(Stage.Nickname);
    }
  }, [isComplete, stage]);

  const { data: gameList } = useQuery({
    queryFn: async (): Promise<GameState> => {
      const res: GameList = await fetch("/api/game/list").then((r) => r.json());

      const state: GameState = {};

      res?.forEach((item) => {
        const { name, startTime, GameRecord } = item;
        state[name] = {
          available: new Date(startTime).getTime() <= Date.now(),
          played: !!GameRecord.length,
        } satisfies GameState[number];
      });

      return state;
    },
    queryKey: ["game", "list"],
  });

  const { mutate: createRecord } = useMutation({
    mutationFn: async (score: number) => {
      const response = await fetch("/api/record/new", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ score, nickname, gameId }),
      });

      if (response.ok) {
        const result = await response.json();
        return result;
      } else {
        throw new Error("Failed to create record");
      }
    },
    onSuccess: (data) => {
      if (data?.id) {
        setGameRecordId(data.id);
      }
    },
    onError: (error) => {
      console.error("Error creating record:", error);
    },
  });

  const onGameEnd = useCallback(
    (score: number) => {
      createRecord(score);
      setScore(score);
      setTimeout(() => setStage(Stage.Result), 1000);
    },
    [createRecord],
  );

  if (stage === Stage.Loading) {
    return <LoadingStage progress={progress} />;
  }

  if (stage === Stage.Nickname) {
    return (
      <NicknameStage
        nickname={nickname}
        onNicknameChange={setNickname}
        onSubmit={() => setStage(Stage.Entrance)}
      />
    );
  }

  if (stage === Stage.Entrance) {
    return (
      <EntranceStage
        gameState={gameList}
        onGameSelect={(gameId) => {
          setGameId(gameId);
          setStage(Stage.Instruction);
        }}
      />
    );
  }

  if (stage === Stage.Instruction) {
    return (
      <InstructionStage gameId={gameId} onStart={() => setStage(Stage.Play)} />
    );
  }

  if (stage === Stage.Play) {
    return <PlayStage gameId={gameId} onGameEnd={onGameEnd} />;
  }

  if (stage === Stage.Result) {
    return (
      <GameResult
        gameTitle={<GameTitle gameId={gameId} />}
        nickname={nickname}
        gameId={gameId}
        score={score}
        gameRecordId={gameRecordId}
        onPlayAgain={() => {
          setGameRecordId("");
          setStage(Stage.Play);
        }}
      />
    );
  }
};

export default GamePage;
