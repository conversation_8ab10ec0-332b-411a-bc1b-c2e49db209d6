"use client";
import { useState } from "react";
import { imageUrl } from "@/utils/image-url";
import { AppButton } from "@/app/components/buttons/app-button";
import { FullScreenImage } from "@/app/components/full-screen";
import { ContentArea } from "@/app/components/content-area";
import { Recaptcha } from "@/app/components/recaptcha";
import { TextInput, Checkbox } from "./input-fields";
import { SuccessDialog } from "./success-dialog";
import { redirect } from "next/navigation";

const PersonalPage = () => {
  const [recaptchaToken, setRecaptchaToken] = useState<string | null>(null);
  const [showSuccess, setShowSuccess] = useState(false);

  const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    if (!recaptchaToken) {
      alert("請完成人機驗證");
      return;
    }
    const formData = new FormData(e.target as HTMLFormElement);
    console.log("Form data:", Object.fromEntries(formData));
    console.log("Recaptcha token:", recaptchaToken);
    setShowSuccess(true);
  };

  return (
    <>
      <FullScreenImage src={imageUrl("/screen-page.png")} />
      <div className="flex flex-col items-center relative pt-[35vw] text-center">
        <ContentArea className="h-[116vw] pt-[5vw] pb-[5vw]">
          <h2 className="text-[6vw] font-[1000] text-[var(--primary-color)] mb-[1.5vw]">
            請填寫抽獎資料
          </h2>

          <form
            name="prize-form"
            className="flex flex-col gap-[3vw]"
            onSubmit={onSubmit}
          >
            <div className="w-[60vw] mx-auto flex flex-col gap-[3vw]">
              <TextInput name="luckyDrawName" label="姓名" />
              <TextInput name="luckyDrawAge" label="年齡" type="number" />
              <TextInput name="luckyDrawPhone" label="電話" type="tel" />
              <TextInput name="luckyDrawAddress" label="地址" />
            </div>

            <div className="mx-auto">
              <Checkbox className="text-[#fff100]">
                <a
                  href="https://"
                  target="_blank"
                  className="underline underline-offset-2 leading-[1em]"
                >
                  個資隱私同意條款
                </a>
              </Checkbox>
            </div>

            <div className="h-[68px]">
              <Recaptcha
                className="mx-auto scale-80"
                onVerify={(token) => setRecaptchaToken(token || "")}
                size="normal"
                theme="light"
              />
            </div>

            <AppButton className="mx-auto" disabled={!recaptchaToken}>
              送出資料
            </AppButton>
          </form>
        </ContentArea>
      </div>
      {showSuccess && (
        <SuccessDialog
          onClose={() => {
            setShowSuccess(false);
            redirect("/");
          }}
        />
      )}
    </>
  );
};

export default PersonalPage;
